map $http_host $MAGE_RUN_CODE {
   magento247.loc base;
}

upstream fastcgi_backend {
     server php:9000;
 }

 server {

     listen 80;

     server_name magento247.loc;

     set $MAGE_ROOT /var/www/magento247;

     set $MAGE_MODE developer;
     set $MAGE_RUN_TYPE website;
     include /var/www/nginx.conf;
     #include /var/www/magento247/nginx.conf.sample;
     fastcgi_param MAGE_MODE $MAGE_MODE;
     fastcgi_param MAGE_RUN_TYPE $MAGE_RUN_TYPE;
     fastcgi_param MAGE_RUN_CODE $MAGE_RUN_CODE;

     error_log /var/log/nginx/error.log;
     access_log /var/log/nginx/access.log;
 }

