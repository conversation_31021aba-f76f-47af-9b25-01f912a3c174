version: '3'

services:

    php:
        container_name: php
        build:
            context: ./docker/php
        ports:
            - 9000:9000
        volumes:
            - appdata:/var/www
            - ./magento247:/var/www/magento247:cached
            - ./docker/php/www.conf:/usr/local/etc/php-fpm.d/www.conf
        environment:
            MYSQL_USER: ${MYSQL_USER}
            MYSQL_PASSWORD: ${MYSQL_PASSWORD}
            PHP_XDEBUG_ENABLED: 1
            XDEBUG_CONFIG: remote_host=host.docker.internal
            PHP_IDE_CONFIG: "serverName=magento247"
        command: >
            sh -c "chown -R www-data:www-data /var/www/magento247 && php-fpm"
        networks:
        - magento_network    
 
    nginx:
        container_name: nginx
        image: nginx:1.24
        ports:
            - 80:80
        volumes:
            - ./magento247:/var/www/magento247:cached
            - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
            - ./docker/nginx/m2-config/nginx.conf:/var/www/nginx.conf
        depends_on:
            - php
        networks:
            - magento_network    
 
    db:
        container_name: mariadb
        image: mariadb:10.6
        ports:
            - 3306:3306
        environment:
            MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
            MYSQL_DATABASE: ${MYSQL_DATABASE}
            MYSQL_USER: ${MYSQL_USER}
            MYSQL_PASSWORD: ${MYSQL_PASSWORD}
        volumes:
            - dbdata:/var/lib/mysql
        networks:
            - magento_network 

    mailhog:
        container_name: mail
        image: mailhog/mailhog:latest
        links:
            - php
        ports:
            - "1025:1025"
            - "8025:8025"
        networks:
            - magento_network    
            
    redis:
        container_name: redis
        image: redis:latest
        networks:
            - magento_network
        
    redisinsight:
        container_name: redisinsight
        image: redislabs/redisinsight:1.14.0
        ports:
        - "8001:8001"
        restart: always
        networks:
        - magento_network  

    rabbitmq:
        container_name: rabbitmq
        image: rabbitmq:3-management
        ports:
            - "15672:15672"
            - "5672:5672"
        networks:
            - magento_network

    opensearch:
        image: opensearchproject/opensearch:2.12.0
        container_name: opensearch
        ports:
            - "9200:9200"
            - "9300:9300"
        environment:
            "discovery.type": "single-node"
            OPENSEARCH_INITIAL_ADMIN_PASSWORD: ${OPENSEARCH_INITIAL_ADMIN_PASSWORD}
            DISABLE_SECURITY_PLUGIN: "true" # Fixed: Wrapped in quotes
        networks:
            - magento_network
    
    opensearch-dashboards:
        image: opensearchproject/opensearch-dashboards:2.12.0
        container_name: opensearch-dashboards
        ports:
            - 5601:5601
        expose:
            - '5601'
        environment:
            OPENSEARCH_HOSTS: '["http://opensearch:9200"]'
            DISABLE_SECURITY_DASHBOARDS_PLUGIN: "true" # Fixed: Wrapped in quotes
        networks:
            - magento_network

    #blackfire:
    #    container_name: blackfire
    #    image: blackfire/blackfire
    #    ports: ["8707"]
    #    environment:
    #        # Exposes BLACKFIRE_* environment variables from the host
    #        BLACKFIRE_SERVER_ID: ### Add server id ####
    #        BLACKFIRE_SERVER_TOKEN: ### Add server token ###
    #        BLACKFIRE_CLIENT_ID: ### Add client id ###
    #        BLACKFIRE_CLIENT_TOKEN: ### Add client Token ###

volumes:
    appdata:
    dbdata:
networks:
 magento_network:
    driver: bridge